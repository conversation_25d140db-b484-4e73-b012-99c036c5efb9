import re
from typing import List
import os
import sys

if __name__ == "__main__":
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(script_dir))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)

from resource.regex_patterns.num_patterns import COMPLEX_NUMBER_PATTERN

DURATION_TIME_RANGE_PREFIX = "duration_time_range"

# =========== 时间单位定义 ===========
class TimeUnits:
    """时间单位定义类"""

    # 时间单位映射（原始单位 -> 标准单位）
    UNIT_MAPPING = {
        # 年相关
        "年": "年", "周年": "年", "年度": "年", "财年": "年", "会计年度": "年", "年份": "年",
        "整年": "年", "个年度": "年", "個年度": "年", "年期": "年",

        # 季度相关
        "季度": "季度", "季": "季度", "整季度": "季度", "个整季": "季度", "个季度": "季度", "个财季": "季度",
        "財季": "季度", "财季": "季度", "季报周期": "季度", "季报": "季度", "季度期": "季度", "季度周期": "季度",

        # 月相关
        "月": "月", "个月": "月", "個月": "月", "整月": "月", "个月份": "月",
        "月度": "月", "财月": "月", "满月": "月", "整个月": "月",
        "个月周期": "月", "个完整月": "月", "月期": "月", "个整月": "月", "個整月": "月",

        # 周相关
        "周": "周", "星期": "周", "礼拜": "周", "整周": "周", "週": "周", "个整周": "周",
        "个礼拜": "周", "个星期": "周", "個周": "周", "禮拜": "周", "整星期": "周",
        "交易周": "周", "周末": "周", "完整周": "周", "周周期": "周", "交易周期": "周",

        # 天相关
        "天": "天", "日": "天", "整天": "天", "个整天": "天",
        "工作日": "天", "自然日": "天", "个工作日": "天", "个自然日": "天", "交易日": "天",
        "营业日": "天", "營業日": "天", "交易天": "天", "结算日": "天", "結算日": "天",
        "个交易日": "天", "个营业日": "天", "个營業日": "天", "个交易天": "天", "个结算日": "天", "个結算日": "天",
        "全天": "天", "日期": "天", "日子": "天", "白天": "天", "日间": "天", "全日": "天",
    }

    # 时间单位转换为天数的比例
    DAYS_CONVERSION = {
        "年": 366,
        "月": 30,
        "季度": 90,
        "周": 7,
        "天": 1
    }

    @classmethod
    def get_all_units(cls) -> List[str]:
        """获取所有时间单位"""
        return list(cls.UNIT_MAPPING.keys())

    @classmethod
    def get_standard_unit(cls, unit: str) -> str:
        """获取标准化的时间单位"""
        return cls.UNIT_MAPPING.get(unit, unit)

    @classmethod
    def get_pattern(cls) -> str:
        """获取时间单位的正则表达式模式"""
        return f"({'|'.join(cls.get_all_units())})"

# 时间单位模式（使用TimeUnits类中的定义）
TIME_UNIT_PATTERN = TimeUnits.get_pattern()

# 时间范围连接词
TIME_RANGE_CONNECTOR = r'(到|至|~|-|—|－|→|、|和|与|或|及|–|⁓|‐|‑|‒|∼|～|∿|➝|➞|⟶|⇒|_|⁃|…)'

# 复合时间连接词：用于连接多个时间单位形成复合时间
TIME_COMPOUND_CONNECTOR = r'(零|又|和|加|以及|再)'

# =========== 持续时间提取器常量 ===========

# 近似修饰前缀：出现在时间单位前的近似修饰词
DURATION_APPROXIMATE_PREFIX = r'(大约|大概|约|约为|接近|差不多|估计|粗略|基本|大致|几乎|将近|可能|或许|差点|将|近|快|近乎)'

# 近似修饰后缀：出现在时间单位后的近似修饰词
DURATION_APPROXIMATE_SUFFIX = r'(左右|上下|内外|之间|出头|之久|多|许|不到|不足|开外|有余|上下浮动|上下波动|大概)'

# 其他修饰后缀：集合了各类修饰词，用于模式匹配但不影响值的计算
DURATION_OTHER_SUFFIX = r'(的时间|的期限|的期间|的周期|时间|期限|期间|周期|的跨度|的长度|的过程|的进程|的阶段|的间隔|的持续时间|的范围|的区间)'

# 上限前缀修饰词：放在时间单位前，表示时间的最大限制
DURATION_UPPER_LIMIT_PREFIX = r'(不超过|不超過|少于|少於|不到|不足|最多|顶多|頂多|低于|低於|短於|短于|小于|小于等于|不会超过|不会多于|不会多於|充其量|至多|最长|最长不超过|上限是|不会超出|最大|最大不超过|控制在|不高于|不长于|不長於|最高|不大於|上限为|封顶|封頂|极限是|最多不超过|最多也就|极限|最多是|≤|<=|≦|<|＜)'

# 上限后缀修饰词：放在时间单位后，表示时间的最大限制
DURATION_UPPER_LIMIT_SUFFIX = r'(内|以下|以内|之内|及以下|及以内|及之内|未满|不到|不足|为限|为界|为上限|内完成|内结束|内搞定|内完工|内解决)'

# 下限前缀修饰词：放在时间单位前，表示时间的最小限制
DURATION_LOWER_LIMIT_PREFIX = r'(最少|至少|不少于|不少於|多于|多於|起码|超过|高于|大于|大于等于|高於|长于|长於|不低于|不少于|不低於|不短於|不短於|少不了|至少需要|需要|须|必须|要|怎么也得|最起码|最低|底线是|不会少于|下限是|最小|起步|保底|不小于|最低要求|起码要|最低限度|最低是|门槛是|不能少于|最低标准|>|≥|>=|＞|≧|⩾)'

# 下限后缀修饰词：放在时间单位后，表示时间的最小限制
DURATION_LOWER_LIMIT_SUFFIX = r'(以上|之上|及以上|及之上|开外|起|起步|打底|为下限|为基础|为底线|以外|为起点|或更长|或更多|才可以|往上|或更久)'

# 否定前缀：表示对后续时间表达的否定
DURATION_NEGATION_PREFIX = r'(不能|不会|不应该|不应|不可以|不可|不能够|不允许|不准|不得|禁止|严禁|不宜|不要|切忌|切莫|切勿|别|莫|勿|毋)'

# 中缀模式特殊前缀
DURATION_FIRST_PREFIX = r'(短则|至少|最少|最短|短期内|短期来看|短的话|往短了说|保守估计|保守来看|最快|快则|短期|初期|早期|前期|开始阶段)'

# 中缀模式特殊后缀
DURATION_SECOND_PREFIX = r'(长则|最多|最长|长期来看|长的话|往长了说|乐观估计|乐观来看|最慢|慢则|长期|后期|晚期|末期|结束阶段)'

# 定义中缀分隔符
DURATION_INFIX_SEPARATOR = r'(?:[,，;；、:：]|\s+|而|但|但是|\n|\r\n)?\s*'

# 短期
# 定义各种子模式
# 1. 短形容词（表示时间短的形容词）
SHORT_ADJ = r'(?:短|较短|很短|超短|极短|稍短|偏短|略短|相当短|特别短|非常短)'
# 2. 小形容词（表示规模小，需搭配周期名词）
SMALL_ADJ = r'(?:小)'
# 3. 短期专用形容词
SHORT_TERM_ADJ = r'(?:短线|短期|超短线|临时|短暂|一下子|一会儿)'
# 4. 程度修饰词
MODIFIERS = r'(?:点|一点|些|一些|点儿)'
# 5. 理财名词
FINANCE_NOUNS = r'(?:理财|投资|产品|项目|标的|品种|操作|策略)'
# 6. 场景修饰词
SCENE_MODIFIERS = r'(?:内|来看|来说|而言|考虑)'
# 7. 短期时间单位
SHORT_TIME_UNITS = r'(?:天|日|小时|钟头|星期|礼拜|周)'
# 8. 快速操作动词
QUICK_ACTION_VERBS = r'(?:到期|回本|兑现|赎回|变现)'

# 中期
# 定义各种子模式
# 1. 中等时长形容词
MIDDLE_ADJ = r'(?:中|中等|适中|适度|一般|普通|不长不短|不多不少|刚刚好|刚好)'
# 2. 中期专用形容词
MIDDLE_TERM_ADJ = r'(?:中期|中线|中长线|中短期|中长期|中期偏长|中期偏短)'
# 3. 近期修饰词
RECENT_ADJ = r'(?:最近|近期)'
# 4. 中期时间单位
MIDDLE_TIME_UNITS = r'(?:月|个月)'
# 5. 中期特定表达
MIDDLE_SPECIFIC_EXPR = r'(?:一时半会|一时三刻|这段时间|一段时间|一阵子|这一阵子|一阵|这阵|这一段|这一段时间)'

# 长期
# 定义长期特有的子模式
# 1. 长时间形容词
LONG_ADJ = r'(?:长|久|长久|较长|很长|超长|相当长|特别长|极长|长远|稍长|偏长|略长)'
# 2. 长期专用形容词
LONG_TERM_ADJ = r'(?:长期|长线|永久|恒久|持久|长久)'
# 3. 长期时间标识词
LONG_SPECIFIC_EXPR = r'(?:一直|始终|永远|永久|一辈子|长久|长时间|很长时间|老是|总是|很久|许久|相当久|很长一段时间|好长时间)'
# 4. 长期时间单位
LONG_TIME_UNITS = r'(?:年)'
# 5. 多年表达
LONG_TIME_MANY_YEARS = r'(?:很多年|许多年|好几年)'
# 6. 存放动词
STORAGE_VERBS = r'(?:放着|存放|放置|搁置|存|放|闲置)'

# 短中长期公共模式
# 1. 闲置词汇 - 表示资金暂时不使用
IDLE_WORDS = r'(?:不用|用不到|不需要|用不着|闲置|搁置|不会用到|不会用|放一边|都不用|都用不到|都不会用到|可用|不(?:需要|会|想|用)用|不打算用)'
# 2. 周期名词 - 表示时间周期的名词
TIME_NOUNS = r'(?:期|周期|时间|时长|阶段|期限|封闭期|投资期|锁定期)'
# 3. 暂缓词汇 - 表示延迟使用
DELAY_WORDS = r'(?:暂缓|暂时|暂且|稍等|稍后|延后|缓一缓)'
# 4. 包含词汇 - 表示范围内
INCLUDE_WORDS = r'(?:内|之内|以内)'
# 5. 助词"的地得"
DE = r'(?:的|地|得)'
# 6. 范围修饰词
SUFFIX_MODIFIERS = r'(?:左右|上下|内|之内|以内)'
# 7. 模糊数量词
FUZZY_QUANTITY = r'(?:几|一些|一两|两三|三五|数|数个|几个)'

# 时间周期标识词
# 1. 周度时间标识词
WEEK_TIME_INDICATORS = r'(?:周度|按周度?|周度性|周度级)'

# 2. 月度时间标识词
MONTH_TIME_INDICATORS = r'(?:月度|按月度?|月度性|月度级)'

# 3. 季度时间标识词
QUARTER_TIME_INDICATORS = r'(?:季度|按季度?|季度性|季度级)'

# 4. 年度时间标识词
YEAR_TIME_INDICATORS = r'(?:年度|按年度?|年度性|年度级)'

# 周度（7天）
WEEK_PATTERN = rf'{WEEK_TIME_INDICATORS}(?:的|型|计|为?单位|为?周期|为?期限)?的?{FINANCE_NOUNS}?'

# 月度（30天）
MONTH_PATTERN = rf'{MONTH_TIME_INDICATORS}(?:的|型|计|为?单位|为?周期|为?期限)?的?{FINANCE_NOUNS}?'

# 季度（90天）
QUARTER_PATTERN = rf'{QUARTER_TIME_INDICATORS}(?:的|型|计|为?单位|为?周期|为?期限)?的?{FINANCE_NOUNS}?'

# 年度（366天）
YEAR_PATTERN = rf'{YEAR_TIME_INDICATORS}(?:的|型|计|为?单位|为?周期|为?期限)?的?{FINANCE_NOUNS}?'

# 组合短期模式
SHORT_TERM_PATTERN = r'(' + '|'.join([
    # 模式1: 短形容词|小形容词 + 周期名词 + 场景修饰(可选) + 闲置词汇(可选) + 连接词(可选) + 理财名词(可选)
    # 例：短期、短时间的理财、小周期的投资等
    f'(?:{SHORT_ADJ}|{SMALL_ADJ}){TIME_NOUNS}{SCENE_MODIFIERS}?{DE}?{IDLE_WORDS}?{DE}?{FINANCE_NOUNS}?',

    # 模式2: 短形容词 + 程度修饰词(可选) + 连接词(可选) + 理财名词
    # 例：短的理财、短一点的理财、很短的产品等
    f'{SHORT_ADJ}{MODIFIERS}?{DE}{FINANCE_NOUNS}',

    # 模式3: 周期名词 + 短形容词|小形容词 + 程度修饰词(可选) + 连接词(可选) + 理财名词(可选)
    # 例：时间短、周期短的理财等
    f'{TIME_NOUNS}(?:{SHORT_ADJ}|{SMALL_ADJ}){MODIFIERS}?{DE}?{FINANCE_NOUNS}?',

    # 模式4: 短期专用形容词 + 闲置词汇(可选) + 连接词(可选) + 理财名词
    # 例：短线理财、短期不用的投资、临时的理财等
    f'{SHORT_TERM_ADJ}{DE}?{IDLE_WORDS}?{DE}?{FINANCE_NOUNS}',

    # 模式5: 模糊数量词 + 短期时间单位 + 包含词汇(可选) + 连接词(可选) + 理财名词(可选)
    # 例：几天、几个周内、两三天的理财等
    f'{FUZZY_QUANTITY}{SHORT_TIME_UNITS}{INCLUDE_WORDS}?{DE}?{IDLE_WORDS}?{DE}?{FINANCE_NOUNS}?',

]) + ')'

# 组合中期模式
MIDDLE_TERM_PATTERN = r'(' + '|'.join([
    # 模式1: 中等时长形容词 + 周期名词 + 场景修饰(可选) + 连接词(可选) + 理财名词
    # 例：中期、适中时间、一般周期的理财等
    f'{MIDDLE_ADJ}{TIME_NOUNS}{SCENE_MODIFIERS}?{DE}?{FINANCE_NOUNS}',

    # 模式2: 中期专用形容词 + 周期名词(可选) + 连接词(可选) + 理财名词(可选)
    # 例：中期、中线、中长期的产品等
    f'{MIDDLE_TERM_ADJ}{TIME_NOUNS}?{DE}?{FINANCE_NOUNS}?',

    # 模式3: 周期名词 + 中等时长形容词 + 连接词(可选) + 理财名词(可选)
    # 例：中期、中线、中长期的产品等
    f'{TIME_NOUNS}{MIDDLE_ADJ}{DE}?{FINANCE_NOUNS}',

    # 模式4: 近期修饰词(可选) + 中期特定表达 + 包含词汇(可选) + 闲置词汇 + 连接词(可选) + 理财名词(可选)
    # 例：最近一段时间不用、近期这阵子闲置的资金等
    f'{RECENT_ADJ}?{MIDDLE_SPECIFIC_EXPR}{INCLUDE_WORDS}?{DE}?{IDLE_WORDS}{DE}?{FINANCE_NOUNS}?',

    # 模式5: 模糊数量词 + 中期时间单位 + 连接词(可选) + 周期名词(可选) + 范围修饰词(可选) + 闲置词汇 + 连接词(可选) + 理财名词(可选)
    # 例：几个月、两三个月左右、几个月内不用的资金等
    f'{FUZZY_QUANTITY}{MIDDLE_TIME_UNITS}{DE}?{TIME_NOUNS}?{SUFFIX_MODIFIERS}?{DE}?{IDLE_WORDS}{DE}?{FINANCE_NOUNS}?',

    # 模式6: 存放动词 + 模糊数量词 + 中期时间单位 + 连接词(可选) + 理财名词(可选)
    # 例：放几个月、存两三个月的理财等
    f'{STORAGE_VERBS}{FUZZY_QUANTITY}{MIDDLE_TIME_UNITS}{DE}?{FINANCE_NOUNS}?',

    # 模式7: 存放动词 + 模糊数量词 + 中期时间单位 + 连接词(可选) + 理财名词(可选)
    # 例：暂时不需要用的理财等
    f'{DELAY_WORDS}{MIDDLE_SPECIFIC_EXPR}?{DE}?{IDLE_WORDS}{DE}?{FINANCE_NOUNS}?'

]) + ')'

# 组合长期模式
LONG_TERM_PATTERN = r'(' + '|'.join([
    # 模式1: 长时间形容词 + 周期名词 + 场景修饰词(可选) + 连接词(可选) + 理财名词(可选)
    # 例：长期、长时间内、长久来看的投资等
    f'{LONG_ADJ}{TIME_NOUNS}{SCENE_MODIFIERS}?{DE}?{IDLE_WORDS}?{DE}?{FINANCE_NOUNS}?',

    # 模式2: 长时间形容词 + 程度修饰词(可选) + 连接词 + 理财名词
    # 例：长点、长一些、长一点的理财等
    f'{LONG_ADJ}{MODIFIERS}?{DE}{FINANCE_NOUNS}',

    # 模式3: 周期名词 + 长时间形容词 + 程度修饰词(可选) + 连接词(可选) + 理财名词(可选)
    # 例：时间长、周期长点的产品等
    f'{TIME_NOUNS}{LONG_ADJ}{MODIFIERS}?{DE}?{FINANCE_NOUNS}?',

    # 模式4: 长期专用形容词 + 闲置词汇(可选) + 连接词(可选) + 理财名词
    # 例：长期、长线投资、持久型理财等
    f'{LONG_TERM_ADJ}{DE}?{IDLE_WORDS}?{DE}?{FINANCE_NOUNS}',

    # 模式5: 长时间形容词 + 周期名词 + 连接词(可选) + 存放动词 + 连接词(可选) + 理财名词(可选)
    # 例：长时间存放、长期地保存的资金等
    f'{LONG_ADJ}{TIME_NOUNS}{DE}?{STORAGE_VERBS}{DE}?{FINANCE_NOUNS}?',

    # 模式6: 长期时间标识词 + 闲置词汇 + 连接词(可选) + 理财名词(可选)
    # 例：一直不用、永久闲置的资金等
    f'{LONG_SPECIFIC_EXPR}{DE}?{IDLE_WORDS}{DE}?{FINANCE_NOUNS}?',

    # 模式7: 多年表达 + 周期名词(可选) + 包含词汇(可选) + 闲置词汇 + 理财名词(可选)
    # 例：很多年内、许多年不用等
    f'{LONG_TIME_MANY_YEARS}{TIME_NOUNS}?{INCLUDE_WORDS}?{DE}?{IDLE_WORDS}{DE}?{FINANCE_NOUNS}?',

    # 模式8: 模糊数量词 + 长期时间单位 + 周期名词(可选) + 包含词汇(可选) + 闲置词汇 + 理财名词(可选)
    # 例：几年、两三年内、几年不用等
    f'{FUZZY_QUANTITY}{LONG_TIME_UNITS}{TIME_NOUNS}?{INCLUDE_WORDS}?{DE}?{IDLE_WORDS}{DE}?{FINANCE_NOUNS}?',

    # 模式9: 存放动词 + 模糊数量词 + 长期时间单位 + 闲置词汇(可选) + 理财名词(可选)
    # 例：放几年、搁置一两年等
    f'{STORAGE_VERBS}{FUZZY_QUANTITY}{LONG_TIME_UNITS}{DE}?{IDLE_WORDS}?{DE}?{FINANCE_NOUNS}?',

    # 模式10: 存放动词 + 多年表达 + 闲置词汇(可选) + 理财名词(可选)
    # 例：放很多年、存许多年等
    f'{STORAGE_VERBS}{LONG_TIME_MANY_YEARS}{DE}?{IDLE_WORDS}?{DE}?{FINANCE_NOUNS}?',

    # 模式11: 存放动词 + 多年表达 + 闲置词汇(可选) + 理财名词(可选)
    # 例：放长一点、存放久一点等
    f'{STORAGE_VERBS}{LONG_ADJ}{MODIFIERS}{DE}?{IDLE_WORDS}?{DE}?{FINANCE_NOUNS}?',

    # 模式12: 长期专用形容词 + 连接词(可选) + 存放动词 + 连接词(可选) + 理财名词(可选)
    # 例：长久存放、持久地保存的资金等
    f'{LONG_TERM_ADJ}{DE}?{STORAGE_VERBS}{DE}?{FINANCE_NOUNS}?'

]) + ')'

# =========== 持续时间提取器常量 ===========

# T+0
T_PLUS_0_PATTERN = r'(T\+0|T0|T0交易|T0赎回)'

# T+1
T_PLUS_1_PATTERN = r'(T\+1|T1|T1赎回)'

# 成立以来模式
# 将模式分解为逻辑组件，提高可读性和可维护性
FOUNDATION_VERBS = r'(?:成立|创建|设立|开设|开业|建立|发布|上线|推出|运行|发行|开展|诞生|组建|创立)'
# 重新定义，消除重叠
PREFIX_WORDS = r'(?:自|从)'
TIME_SUFFIX = r'(?:以来|至今|迄今|迄今为止|起|开始|之后|后|来)'

# 重新组合模式，更加清晰和准确：
# 1. 带前缀的表达式: "自成立以来"、"从设立起"等
# 2. 不带前缀的表达式: "成立以来"、"创建至今"等
# 3. 特殊固定表达式: 不适合上述规则的特殊情况
FOUNDED_SINCE_PATTERN = r'(' + '|'.join([
    # 模式1: 带前缀和后缀的表达 (自成立以来、从设立起等)
    f'{PREFIX_WORDS}{FOUNDATION_VERBS}{TIME_SUFFIX}',

    # 模式2: 仅动词和后缀 (成立以来、创建至今等)
    f'{FOUNDATION_VERBS}{TIME_SUFFIX}',

    # 模式3: 特殊固定表达
    r'设立时|起始至今|自创立'
]) + ')'


# 半单位模式（"半年"、"半个月"等）
HALF_UNIT_PATTERN = fr'半(?:个|個)?({TIME_UNIT_PATTERN})'

# 单位加半模式（"一年半"、"两月半"等）
UNIT_HALF_PATTERN = fr'({COMPLEX_NUMBER_PATTERN})({TIME_UNIT_PATTERN})半'

# 简单时间
SIMPLE_TIME_PATTERN = fr'{COMPLEX_NUMBER_PATTERN}{TIME_UNIT_PATTERN}'

# 复合时间
COMPLEX_TIME_PATTERN = fr'({SIMPLE_TIME_PATTERN})({TIME_COMPOUND_CONNECTOR}?({SIMPLE_TIME_PATTERN}))+'

# 定义基本时间单元模式
TIME_UNIT_EXPR = fr'''
(?:
  # 复合时间（如"1年3个月"）
  (?:{COMPLEX_TIME_PATTERN})
  |
  # 单位加半表达式（如"一年半"）
  (?:{UNIT_HALF_PATTERN})
  |
  # 半单位表达式（如"半年"、"半个月"）
  (?:{HALF_UNIT_PATTERN})
  |
  # 简单时间单位（数字+单位）
  (?:{COMPLEX_NUMBER_PATTERN}{TIME_UNIT_PATTERN})
)
'''

# 时间范围模式
DURATION_SUPER_PATTERN_STR = fr'''
(?:
  # 否定前缀（可选）
  (?P<negation_prefix>{DURATION_NEGATION_PREFIX})?

  # 近似前缀修饰词（可选）
  (?P<approximate_prefix>{DURATION_APPROXIMATE_PREFIX})?

  # 上限前缀修饰词（可选）
  (?P<upper_limit_prefix>{DURATION_UPPER_LIMIT_PREFIX})?

  # 下限前缀修饰词（可选）
  (?P<lower_limit_prefix>{DURATION_LOWER_LIMIT_PREFIX})?

  (?:
    # 格式1：时间单元+连接词+时间单元
    (?P<time_unit1>{TIME_UNIT_EXPR})
    (?P<connector>{TIME_RANGE_CONNECTOR})
    (?P<time_unit2>{TIME_UNIT_EXPR})
    |
    # 格式2：单个时间单元
    (?P<time_unit>{TIME_UNIT_EXPR})
    |
    # 格式3：数字+连接词+数字+时间单元
    (?P<number1>{COMPLEX_NUMBER_PATTERN})
    (?P<connector2>{TIME_RANGE_CONNECTOR})
    (?P<number2>{COMPLEX_NUMBER_PATTERN})
    (?P<shared_unit>{TIME_UNIT_PATTERN})
  )

  # 上限后缀修饰词（可选）
  (?P<upper_limit_suffix>{DURATION_UPPER_LIMIT_SUFFIX})?

  # 下限后缀修饰词（可选）
  (?P<lower_limit_suffix>{DURATION_LOWER_LIMIT_SUFFIX})?

  # 近似后缀修饰词（可选）
  (?P<approximate_suffix>{DURATION_APPROXIMATE_SUFFIX})?

  # 其他后缀修饰（可选）
  (?P<other_suffix>{DURATION_OTHER_SUFFIX})?
)
'''

# 修改中缀持续时间模式
DURATION_RANGE_WITH_INFIX_PATTERN_STR = fr'''
# 第一部分：前缀+时间单元
(?P<first_prefix>{DURATION_FIRST_PREFIX})
(?:的)?
(?P<first_time_unit>{TIME_UNIT_EXPR})

# 中间分隔符
(?P<infix_separator>{DURATION_INFIX_SEPARATOR})

# 第二部分：前缀+时间单元
(?P<second_prefix>{DURATION_SECOND_PREFIX})
(?:的)?
(?P<second_time_unit>{TIME_UNIT_EXPR})
'''

# 组合短期/中期/长期模式
TERM_PATTERN_STR = fr'''
(?:
  (?P<short_term>{SHORT_TERM_PATTERN})
  |
  (?P<middle_term>{MIDDLE_TERM_PATTERN})
  |
  (?P<long_term>{LONG_TERM_PATTERN})
)
'''

# 组合周度/月度/季度/年度模式
PERIOD_PATTERN_STR = fr'''
(?:
  (?P<week_period>{WEEK_PATTERN})
  |
  (?P<month_period>{MONTH_PATTERN})
  |
  (?P<quarter_period>{QUARTER_PATTERN})
  |
  (?P<year_period>{YEAR_PATTERN})
)
'''

# 编译正则表达式
DURATION_SUPER_PATTERN_COMPILED = re.compile(DURATION_SUPER_PATTERN_STR, re.VERBOSE)
DURATION_RANGE_WITH_INFIX_PATTERN_COMPILED = re.compile(DURATION_RANGE_WITH_INFIX_PATTERN_STR, re.VERBOSE)
TERM_PATTERN_COMPILED = re.compile(TERM_PATTERN_STR, re.VERBOSE)
PERIOD_PATTERN_COMPILED = re.compile(PERIOD_PATTERN_STR, re.VERBOSE)
SIMPLE_TIME_PATTERN_COMPILED = re.compile(SIMPLE_TIME_PATTERN, re.VERBOSE)
COMPLEX_TIME_PATTERN_COMPILED = re.compile(COMPLEX_TIME_PATTERN, re.VERBOSE)
HALF_UNIT_PATTERN_COMPILED = re.compile(HALF_UNIT_PATTERN, re.VERBOSE)
UNIT_HALF_PATTERN_COMPILED = re.compile(UNIT_HALF_PATTERN, re.VERBOSE)
TIME_UNIT_PATTERN_COMPILED = re.compile(TIME_UNIT_PATTERN, re.VERBOSE)
TIME_UNIT_EXPR_COMPILED = re.compile(TIME_UNIT_EXPR, re.VERBOSE)

COMPILED_TIME_DURATION_PATTERNS = {
    f"{DURATION_TIME_RANGE_PREFIX}-super_pattern": DURATION_SUPER_PATTERN_COMPILED,
    f"{DURATION_TIME_RANGE_PREFIX}-duration_range_with_infix_pattern": DURATION_RANGE_WITH_INFIX_PATTERN_COMPILED,
    f"{DURATION_TIME_RANGE_PREFIX}-term_pattern": TERM_PATTERN_COMPILED,
    f"{DURATION_TIME_RANGE_PREFIX}-period_pattern": PERIOD_PATTERN_COMPILED,
    f"{DURATION_TIME_RANGE_PREFIX}-time_unit_pattern": TIME_UNIT_PATTERN_COMPILED,
    f"{DURATION_TIME_RANGE_PREFIX}-time_unit_expr": TIME_UNIT_EXPR_COMPILED,
    f"{DURATION_TIME_RANGE_PREFIX}-simple_time_pattern": SIMPLE_TIME_PATTERN_COMPILED,
    f"{DURATION_TIME_RANGE_PREFIX}-complex_time_pattern": COMPLEX_TIME_PATTERN_COMPILED,
    f"{DURATION_TIME_RANGE_PREFIX}-half_unit_pattern": HALF_UNIT_PATTERN_COMPILED,
    f"{DURATION_TIME_RANGE_PREFIX}-unit_half_pattern": UNIT_HALF_PATTERN_COMPILED,
    f"{DURATION_TIME_RANGE_PREFIX}-short_term_pattern": re.compile(SHORT_TERM_PATTERN),
    f"{DURATION_TIME_RANGE_PREFIX}-middle_term_pattern": re.compile(MIDDLE_TERM_PATTERN),
    f"{DURATION_TIME_RANGE_PREFIX}-long_term_pattern": re.compile(LONG_TERM_PATTERN),
    f"{DURATION_TIME_RANGE_PREFIX}-quarter_pattern": re.compile(QUARTER_PATTERN),
    f"{DURATION_TIME_RANGE_PREFIX}-month_pattern": re.compile(MONTH_PATTERN),
    f"{DURATION_TIME_RANGE_PREFIX}-year_pattern": re.compile(YEAR_PATTERN),
    f"{DURATION_TIME_RANGE_PREFIX}-founded_since_pattern": re.compile(FOUNDED_SINCE_PATTERN),
}

if __name__ == "__main__":
    from resource.update_helper import update_patterns_to_es
    update_patterns_to_es(COMPILED_TIME_DURATION_PATTERNS, "tool_regexs")